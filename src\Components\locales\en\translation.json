{"settings": "Settings", "my_profile": "My profile", "notifications": "Notifications", "my_company": "My company", "team_management": "Team management", "plan_billing": "Plan & billing", "profile_picture": "Profile picture", "upload": "Upload", "personal_information": "Personal information", "first_name": "First name", "enter_first_name": "Enter your first name", "first_name_required": "First Name Required", "last_name": "Last name", "enter_last_name": "Enter your last name", "last_name_required": "Last Name Required", "phone_number": "Phone number", "enter_phone_number": "Enter phone number", "language": "Language", "change_password": "Change password", "change_email": "Change email", "save_changes": "Save changes", "saving": "Saving...", "notifications_description": "Notifications are only available for paid plans. Upgrade your plan to receive email notifications when candidates complete assessments.", "notify_me": "Notify me", "email_me_on_completion": "Email me when a candidate completes an assessment", "email_me_daily_summary": "Email me a daily summary of all assessment activity", "daily_summary_time": "(sent at 9am every day)", "enter_company_name": "Enter your company name", "enter_primary_color": "Enter your color", "enter_secondary_color": "Enter your color", "company_logo": "Company Logo", "company_introduction_video": "Company Introduction Video", "video_description": "This video will be shown to candidates before they start their test.", "no_introduction_video": "No Introduction Video", "upload_video_description": "Upload a welcome video to introduce your company and test to candidates.", "upload_video": "Upload Video", "premium_feature": "Premium feature", "change_your_video": "Change Your Video", "video_shown_description": "This video will be shown to candidates prior to taking the test.", "change": "Change", "preview": "Preview", "button": "<PERSON><PERSON>", "text_colour": "Text colour", "in_comparison_to_black": "in comparison to black text", "color_palette_description": "See below how the button and text design elements change as you adjust the colour palette", "current_plan": "Current Plan", "explore_for_free": "Explore for free", "run_active_test": "Run", "active_test": "Active Test", "active_tests": "Active Tests", "unlimited_active_tests": "Unlimited Active Tests", "send_test_to": "Send Test to", "unlimited_candidates": "Unlimited Candidates", "managed_by": "Managed by", "account_user": "Account User", "account_users": "Account Users", "unlimited_account_users": "Unlimited Account Users", "organizations_max_25": "Organizations with max 25 employees", "organizations_25_250": "Organizations with 25-250 employees", "per_month": "per month", "billing_history": "Billing history", "billing_address": "Billing address", "payment_method": "Payment method", "change_password_modal": "Change Password", "old_password": "Old Password", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "old_password_required": "Old Password Required", "new_password_required": "New Password Required", "confirm_password_required": "Confirm Password Required", "password_changed": "Password Changed", "change_email_modal": "Change Email", "current_email": "Current Email", "new_email": "New Email", "current_password": "Current Password", "email_required": "Email Required", "password_required": "Password Required", "email_changed": "Email Changed", "team_members": "TEAM MEMBERS", "search_members": "Search Members", "search_placeholder": "Search", "role": "Role", "all_roles": "All Roles", "remove_your_member": "Remove your member", "remove_member_description": "Are you sure you want to remove this member?", "remove_member": "Remove member", "unsubscribe_package": "Unsubscribe package", "unsubscribe_description": "Are you sure you want to unsubscribe from your package? By doing so, you will lose access to the following features:", "share_your_feedback": "Share your feedback", "delete_your_video": "Delete your video", "delete_video_description": "Are you sure you want to Delete this video", "yes": "Yes", "table_name": "Name", "table_email": "Email", "table_status": "Status", "table_invitation": "Invitation", "table_action": "Action", "owner": "Owner", "admin": "Admin", "editor": "Editor", "viewer": "Viewer", "deactivated": "Deactivated", "active": "Active", "sent": "<PERSON><PERSON>", "activate": "Activate", "activating": "Activating...", "deactivate": "Deactivate", "deactivating": "Deactivating...", "delete_member": "Delete member", "welcome_screen": "Welcome screen", "test_instructions_screen": "Test Instructions screen", "webcam_screen": "Webcam screen", "question_screen": "Question screen", "feedback_screen": "Feedback screen", "brand": "Brand", "intro_video": "Intro Video", "upload_company_introduction_video": "Upload Company Introduction Video", "video_uploaded_successfully": "Video Uploaded Successfully", "video_shown_before_test": "This video will be shown to candidates before they start their test.", "click_upload_drag_drop": "Click to upload or drag and drop your video", "supported_formats": "Supported formats: MP4, MOV, AVI. Maximum file size: 50 MB.", "select_video": "Select Video", "processing_video": "Processing video...", "uploading_video": "Uploading video...", "upload_video_button": "Upload Video", "uploading": "Uploading...", "file_size_exceeds": "File size exceeds 50MB. Please select a smaller file.", "upload_failed": "Failed to upload video. Please try again.", "video_uploaded_success_toast": "Video uploaded successfully", "login": "<PERSON><PERSON>", "register": "Register", "forgot_password": "Forgot Password?", "email": "Email", "enter_email": "Enter your email", "password": "Password", "enter_password": "Enter your password", "remember_me": "Remember me", "sign_in_with_google": "Sign in with Google", "sign_in_with_discord": "Sign in with Discord", "dont_have_account": "Don't have an account?", "create_account": "Create Account", "already_have_account": "Already have an account?", "sign_in": "Sign In", "reset_password": "Reset Password", "confirm_password": "Confirm Password", "enter_confirm_password": "Confirm your password", "reset_password_email_sent": "Reset password email sent", "check_email": "Please check your email for reset instructions", "confirm_email": "Confirm Email", "email_confirmation_sent": "Email confirmation sent", "check_email_confirmation": "Please check your email to confirm your account", "navbar": {"my_tests": "My Tests", "my_candidates": "My Candidates", "modules": "<PERSON><PERSON><PERSON>", "hello": "Hello", "start_tour": "Start Tour", "contact_us": "Contact us", "open_user_menu": "Open user menu", "user_photo": "user photo", "take_site_tour": "Take Site Tour", "tour_description": "Would you like to take a tour of the site?", "no_thanks": "No, thanks", "yes_please": "Yes, please"}, "dashboard": {"create_new_test": "Create New Test", "tests": "Tests", "filter_tests": "Filter Tests", "search_tests": "Search Tests", "draft": "Draft", "active": "Active", "archived": "Archived", "deactivated": "Deactivated", "all": "All", "entries_per_page": "Entries per page", "showing": "Showing", "to": "to", "of": "of", "entries": "Entries", "previous": "Previous", "next": "Next", "no_tests_found": "No tests found", "test_name": "Test Name", "company_name": "Company Name", "created_date": "Created Date", "last_activity": "Last Activity", "status": {"completed": "Completed", "invited": "Invited", "started": "Started", "in_progress": "In Progress"}, "subscription": {"limit_reached": "You've reached your limit!", "free_limit": "Explore subscription allows 1 Active Test, either change the status of your Active Test to 'Archived' or upgrade your subscription package", "starter_limit": "Starter subscription allows 5 Active Tests, either change the status of an Active Test to 'Archived' or upgrade your subscription package"}, "resume_test": "Resume Test", "resume_test_confirmation": "Are you sure you want to resume your test?", "actions": "Actions", "delete_test": "Delete Test", "delete_confirmation": "Are you sure you want to delete this test?", "yes": "Yes", "no": "No", "test_created": "Test created successfully", "test_deleted": "Test deleted successfully", "test_updated": "Test updated successfully", "error_occurred": "An error occurred", "loading": "Loading...", "verification_email_sent": "Verification email sent", "verification_email_error": "Error sending verification email", "resend_verification": "Resend Verification", "start_tour": "Start Tour", "contact_us": "Contact Us", "table": {"test_name": "Test Name", "company_name": "Company Name", "created_date": "Created Date", "actions": "Actions", "candidates": "Candidates", "my_tests": "My Tests", "create_new_test": "Create New Test", "entries_per_page": "Entries per page", "progress": "Progress", "completed": "Completed", "in_progress": "In Progress", "not_completed": "Not Completed", "last_activity": "Last Activity", "date_created": "Date Created", "showing": "Showing", "pages": "Pages", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next", "role_name": "Role Name", "search_tests": "Search tests", "search_tests_placeholder": "Search tests", "tests": "Tests", "verify_email": {"title": "Please verify your email address", "message": "To complete registration, please click the link in the email we have sent you to your provided email address.", "resend": "Click here to send again.", "tooltip": "Please verify your email first 👍"}, "permissions": {"no_create_test": "You do not have permissions to create test"}, "status": {"all": "All", "active": "Active", "draft": "Draft", "archived": "Archived", "deactivated": "Deactivated"}, "hiring_stage": "Hiring stage", "headers": {"id": "ID#", "question": "Question", "correct_options": "Correct options", "type": "Type", "status": "Status", "image": "Image"}, "content": {"yes": "YES", "no": "No", "not_accessible_tooltip": "Questions from Dexta Library are Not Accessible"}, "buttons": {"added": "Added", "add": "Add", "adding": "Adding", "tooltips": {"already_added": "You have added this question already", "max_questions": "You cannot add more than 50 questions", "fill_fields_first": "Please fill out Module Name and Module Time first"}}}}, "candidates_tab": {"title": "Select candidates", "share_link": {"title": "Share invite link:", "info": "Share a unique link with each candidate to track their test completion progress. Remember to regenerate the link for each new candidate", "regenerate_copy": "Regenerate and copy"}, "or": "OR", "tabs": {"invite_by_email": "Invite by email", "invite_in_bulk": "Invite in bulk"}, "invite_by_email": {"description": "Invite multiple candidates by entering their first name, last name and email below", "table": {"headers": {"first_name": "FIRST NAME", "last_name": "LAST NAME", "email": "EMAIL"}, "labels": {"first_name": "First name", "last_name": "Last name", "email": "Email"}, "buttons": {"add": "Add", "save": "Save", "edit": "Edit"}, "toasts": {"email_exists": "Email already exists"}}, "modal": {"title": "You've reached your limit!", "description": "To add more than 5 candidates, upgrade your subscription package to Starter or Pro"}}, "invite_in_bulk": {"description": "Invite multiple candidates at once through a CSV or XLSX file upload.", "keep_in_mind": "Keep in mind...", "instructions": "Make sure you use three columns in your file in the following order: First name, Last name, Email. You can only add 1200 candidates per file.", "example": "Example", "table": {"first_name": "First name", "last_name": "Last name", "email": "Email"}, "download_template": "Download a template here", "drag_and_drop": "Drag and drop or", "browse": "browse", "your_files": "your files", "selected_file": "Selected file: ", "upload_limit": "You can upload up to", "upload_limit_2": "1200 candidates per file.", "supported_file_types_label": "Supported file types:", "supported_file_types": ".csv .xls .xlsx"}, "actions": {"clear_list": "Clear list", "customise_invitation_email": "Customise invitation email", "invite_candidate": "Invite <PERSON>didate", "invite_candidates": "In<PERSON><PERSON>", "inviting": "Inviting"}, "toasts": {"link_copied": "The invitation link has been copied.", "copy_failed": "Co<PERSON> failed"}}, "candidates": {"title": "My Candidates", "search_candidates": "Search candidates", "create_new_test": "Create new test", "verify_email": {"title": "Please verify your email address", "message": "To complete registration, please click the link in the email we have sent you to your provided email address.", "resend": "Click here to send again."}, "permissions": {"verify_email": "Please verify your email first 👍", "no_permission": "You do not have permissions to create test"}, "table": {"name": "Name", "email": "Email", "test": "Test", "status": "Status", "actions": "Actions", "no_candidates": "No candidates found", "loading": "Loading candidates...", "hiring_stage": "Hiring stage", "role_name": "Role name", "score": "Score"}, "status": {"all": "All", "active": "Active", "completed": "Completed", "in progress": "In Progress", "not_started": "Not Started", "expired": "Expired", "interview_completed": "Interview Completed", "candidate_rejected": "Candidate Rejected", "invited": "Invited"}, "delete": {"header": "Delete your candidate", "description": "Are you sure you want to delete your candidate? This action cannot be undone."}, "incomplete_test_message": "This candidate has not completed their test yet. Therefore there are no test results to show."}, "team": "Team", "orders": "Orders", "reports": "Reports", "logout": "Logout", "welcome": "Welcome", "search": "Search", "filter": "Filter", "sort": {"date_invited": "Date invited", "date_completed": "Date completed", "avg_score_l2h": "Average score (low to high)", "avg_score_h2h": "Average score (high to low)"}, "actions": "Actions", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required_field": "This field is required", "invalid_email": "Invalid email address", "password_mismatch": "Passwords do not match", "password_requirements": "Password must be at least 8 characters long", "terms_and_conditions": "Terms and Conditions", "privacy_policy": "Privacy Policy", "accept_terms": "I accept the terms and conditions", "accept_privacy": "I accept the privacy policy", "contains_at_least_one_number": "Contains at least one number", "contains_at_least_one_upper_character": "Contains at least one uppercase character", "contains_at_least_one_special_character": "Contains at least one special character", "contains_at_least_12_characters": "Contains at least 12 characters", "SUBMITTING": "SUBMITTING", "by_creating_an_account_you_confirm_that_you_have_read_and_agree_to_Dexta's": "By creating an account, you confirm that you have read and agree to <PERSON><PERSON>'s", "term_of_use": "Term of Use", "or": "or", "return_to_login": "Return to login", "no_worries_reset_instructions": "No worries, we'll send you reset instructions", "sending_mail": "SENDING MAIL", "create_test": {"title": "Create new test", "added_modules": "Added modules", "mins": "mins", "preview_modules": "Preview modules", "preview_tooltip": "You have to complete at-least two steps to preview your modules", "go_back": "Go back", "next_step": "Next step", "publish_test": "Publish Test", "send_invites": "Send invites", "back_to_my_tests": "Back to My Tests", "processing": "Processing", "steps": {"add_test_details": "Add test details", "add_modules": "Add modules", "add_custom_questions": "Add custom questions", "select_candidates": "Select candidates"}, "general": {"role_name": "Role name", "name_your_role": "Name your role", "role_name_tooltip": "Enter a name for your <PERSON>. This name will be displayed to the candidate when taking the Test.", "test_name": "Test name", "enter_test_name": "Enter test name", "job_role": "Job role", "select_job_role": "Please select a job role", "all_jobs": "All jobs", "job_role_location": "Job role location", "select_job_role_location": "Please select job role location", "experience_level": "Experience level", "experience_tooltip": "Each one of our modules are designed for a specific level of experience. Select a level of experience to see the modules associated with it. Alternatively select 'All Levels' to see modules across all experience levels.", "experience_tooltips": {"beginner": "1-2 years experience", "intermediate": "3-5 years experience", "advanced": "6+ years experience", "all": "All levels of experience"}, "experience_buttons": {"entry_level": "Entry level", "mid_level": "Mid level", "senior_level": "Senior level", "all_levels": "All levels"}, "work_arrangements": "Work arrangements", "select_work_arrangement": "Please select a work arrangement", "work_arrangement_options": {"remote": "Remote", "onsite": "Onsite", "hybrid": "Hybrid"}, "industry": "Industry", "select_industry": "Please select an industry", "all_industries": "All industries", "job_role_title": "Job role title", "enter_job_role_title": "Enter job role title"}, "modules": {"suggested_modules": "Suggested modules", "all_modules": "All modules", "company_modules": "modules", "search_placeholder": "Search test module library here", "search_tooltip": "Type keywords here to search through our test library.", "coming_soon": "Coming soon", "read_more": "Read more", "sample_questions": "<PERSON><PERSON> questions", "remove": "Remove", "add_module": "Add module", "adding": "Adding...", "mins": "mins", "experience_levels": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "all_levels": "All levels", "custom_question": "Custom Questions"}, "add_modules_section": {"title": "Add modules", "description": "Your test can include up to 5 modules. Scroll and select modules on the right. Use different types of modules for the best results.", "you_have_added": "You have added:", "select_module": "Select module"}, "tooltips": {"cannot_add_more_than_5": "You cannot add more than 5 modules", "cannot_add_this_module": "You cannot add this module"}, "no_modules_found": {"message": "No modules found. If you would like us to prioritise creation of a new module, please let us know by", "clicking_here": "clicking here", "turnaround_time": "Our typical turnaround time for a module is 1-2 weeks"}}, "questions": {"add_custom_module": "Add custom module", "description": "Add up to 50 questions to your Custom module. Your Custom module will be the last module your candidate takes. It will be shown to the candidates after the Dexta default modules you have selected in the Add Module section.", "custom_module_name": "Custom Module Name", "name_tooltip": "The name you select for your Custom <PERSON><PERSON><PERSON> will be visible to the candidate", "add_name_placeholder": "Add name here", "custom_module_time": "Custom Module Time", "time_tooltip": "The time you enter here will be used for the countdown timer whilst the candidate is taking this custom Module", "set_max_time": "Set the max time allowed to complete the custom module", "mins": "mins", "add_custom_question": "Add Custom Question", "add_new_question": "Add new question", "tooltips": {"empty_fields": "You cannot proceed with empty Module name and Module time fields", "max_questions": "You cannot add more than 50 questions"}, "add_from_library": "Add custom questions from", "your_library": "Your library", "dexta_library": "Dexta library", "search_library": "Search library", "search_tooltip": "Type keywords here to search through", "your_library_text": "your library", "dexta_library_text": "Dexta library", "general": "General", "your_library_label": "Your Library", "preview": "Preview", "delete": "Delete", "no_custom_questions": "No custom questions available", "delete_modal": {"delete_question": "Delete your question?", "delete_question_description": "Are you sure you want to delete this question permanently from your custom question library", "delete_question_button": "Delete question", "remove_question_button": "Remove question"}, "table": {"headers": {"id": "ID#", "question": "Question", "correct_options": "Correct options", "type": "Type", "status": "Status", "image": "Image"}, "content": {"yes": "YES", "no": "No", "not_accessible_tooltip": "Questions from Dexta Library are Not Accessible"}, "buttons": {"added": "Added", "add": "Add", "adding": "Adding", "tooltips": {"already_added": "You have added this question already", "max_questions": "You cannot add more than 50 questions", "fill_fields_first": "Please fill out Module Name and Module Time first"}}}}}, "questions_modal": {"title": {"edit": "Edit Question", "new": "New question"}, "buttons": {"cancel": "Cancel", "update": "Update", "save": "Save", "saving": "Saving"}, "tooltips": {"no_correct_option": "You haven't selected the correct option yet"}, "sections": {"question": {"title": "Enter multiple choice question", "warning": {"title": "The language of your test is English. Make sure to phrase the question using the same language.", "icon_alt": "Warning icon"}, "placeholder": "For example: What features of a process make it more suitable for Robotics Process Automation?"}, "image": {"title": "Upload image here if your question requires supporting visuals", "add_another": "Add another image", "max_images_tooltip": "You can only add a maximum of two images in a question.", "upload_area": {"title": "Drop files anywhere on this box to upload", "or": "or", "select_files": "Select Files"}}, "answers": {"title": "Select the right answer", "add_another": "Add another answer", "placeholder": "Insert answer"}}, "delete_modal": {"title": "Delete your question", "description": "Are you sure you want to remove this question?", "button": "Delete question"}}, "verify_imports": {"title": "Verify <PERSON>", "issues_title": "The following issues need to be resolved:", "missing_field": "{{field}} is required and must not be empty.", "invalid_email_format": "Email must be a valid email e.g (<EMAIL>, <EMAIL>, <EMAIL>).", "duplicate_emails": "You have duplicate emails. Each email must be unique.", "header_missing": "Please write <strong>{{field}}</strong> in header.", "invalid_headers": "Invalid headers detected. Please use only: <strong>First Name</strong>, <strong>Last Name</strong>, <strong>Email</strong>.", "header_tooltip": "This header is incorrect. Expected headers are: First name, Last name, <PERSON><PERSON>.", "status": "Status", "cancel": "Cancel", "confirm_emails": "Confirm Emails"}, "tests": {"modules": "<PERSON><PERSON><PERSON>", "create_new_test": "Create new test", "limit_reached": "You've reached your limit!", "free_subscription_limit": "Explore subscription allows 1 Active Test, either change the status of your Active Test to 'Archived' or upgrade your subscription package", "starter_subscription_limit": "Starter subscription allows 5 Active Tests, either change the status of an Active Test to 'Archived' or upgrade your subscription package", "please_verify_email": "Please verify your email first 👍", "no_permission_create_test": "You do not have permissions to create test", "search": "Search", "search_module_placeholder": "Search module here...", "filters": "Filters", "time_duration": "Time Duration", "reset_filters": "Reset Filters", "no_modules_found": "No modules found.", "click_here": "clicking here", "modules_available": "{{count}} Modules available in English", "read_more": "Read more", "sample_questions": "Sample Questions", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "all_levels": "All levels", "custom_questions": "Custom questions", "mins": "mins", "covered_skills": "Covered Skills", "relevant_for": "This module is relevant for", "description": "Description", "language": "Language", "english": "English", "level": "Level", "all_levels_of_experience": "All levels of experience", "experience_tooltips": {"beginner": "1-2 years experience", "intermediate": "3-5 years experience", "advanced": "6+ years experience", "all": "All levels of experience", "custom_questions": "Custom questions"}, "no_active_jobs": "No active jobs available", "no_departments": "No departments available", "sidebar": {"category": "Experience Level", "industry": "Industry", "department": "Department", "job_role": "Job Role", "experience_options": {"beginner": "Entry Level", "intermediate": "Mid Level", "advanced": "Senior Level", "all": "All Levels"}}, "tooltips": {"beginner": "1-2 years experience", "intermediate": "3-5 years experience", "advanced": "6+ years experience", "all": "All levels of experience", "verify_email": "Please verify your email first 👍", "no_permission": "You do not have permissions to create test"}, "experience_labels": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "all": "All levels"}}, "invite_candidates": {"try_yourself": "Try yourself", "try_tooltip": "Use this feature to see and experience the test like your candidates do!", "invite": "Invite", "download_results": "Download results", "download_pdf": "Download PDF", "download_csv": "Download CSV", "select_all": "Select All", "select_multiple": "Select Multiple", "download_xlsx": "Download XLSX", "bulk_action": "Bulk action", "sort_by": "Sort by", "deleting": "Deleting", "average_score": "Average score", "test_status": "Test Status", "hiring_stage": "Hiring stage", "candidate_name": "Candidate name", "selected_modules": "Selected modules", "module": "<PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON>", "mins": "mins", "table_status": "Status", "delete_candidate_confirmation": "Are you sure you want to delete this candidate? This action cannot be undone.", "cancel": "Cancel", "delete_candidate": "Delete candidate", "status": {"all": "All", "invited": "Invited", "in_progress": "In Progress", "completed": "Completed"}, "actions": {"select_all": "Select All", "select_multiple": "Select Multiple", "delete_candidate": "Delete candidate", "view_candidate_details": "View candidate details", "send_reminder": "Send reminder", "reminder_cannot_be_sent": "Reminder can't be sent to candidates who completed the test"}, "modals": {"archive_test": "Archive Test", "delete_test": "Delete your test", "clone_test": "Clone your test", "yes": "Yes", "delete": "Delete Test", "clone": "Clone Test", "archive_confirm": "Are you sure you want to archive this test? If you have invited candidates on this test, they would not be able to attempt it if you archive the test.", "delete_confirm": "Are you sure you want to delete your test? This action cannot be undone.", "clone_confirm": "When you clone a test then it goes in the draft from where you can edit it and publish it to invite the candidates"}, "no_candidates_found": "No candidates found", "search_candidates": "Search candidates", "graph_view": "Graph view", "view_details_btn": "View Details", "custom_questions": "Custom questions", "all_levels": "All levels", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "sort": {"date_invited": "Date invited", "date_completed": "Date completed", "avg_score_l2h": "Average score (low to high)", "avg_score_h2h": "Average score (high to low)"}}, "details": {"overall_score": "Overall Score", "interpret_results_title": "Interpret Results", "interpret_results": {"how_to_interpret_test_results": "How to Interpret Test Results", "test_results_page_description": "The test results page provides a detailed overview of a candidate's performance across multiple test modules. Below is a guide to help you understand and interpret the results displayed on the page.", "key_elements_explained": "Key Elements Explained", "overall_scores": "Overall Scores", "total_average_score": "Total Average Score:", "total_average_score_desc": "Represents the total performance of the candidate across all modules.", "candidate_pool_average_score": "Candidate Pool Average Score:", "candidate_pool_average_score_desc": "The average score achieved by all your candidates who took the same test.", "highest_candidate_score": "Highest Candidate Score:", "highest_candidate_score_desc": "Indicates the highest score achieved by any candidate in your candidate pool.", "your_best": "Your best", "candidate": "candidate", "score": "score", "john_doe_total": "<PERSON>'s", "total": "total", "your_candidate": "Your candidate", "pool_average": "Pool average", "your_candidate_pool_average": "Your candidate pool average", "module_specific_results": "Module-Specific Results", "module_specific_comparative_data_points": "Module Specific Comparative Data Points", "performance_completion_time_insights": "Performance & Completion Time Insights", "anti_cheating_monitor": "Anti-Cheating Monitor", "module_specific_results_desc": "This section explains the results for each module, including title, level, proficiency, score, average score, and time spent.", "module_title": "Module Title:", "module_title_desc": "The title and level of the module (e.g., 'Robotics Process Automation L1').", "proficiency_level": "Proficiency Level:", "proficiency_level_desc": "Indicated by tags like 'Be<PERSON>ner', 'Intermediate', or 'Advanced'.", "candidate_score": "Candidate's Score:", "candidate_score_desc": "Number of correct answers achieved by the candidate against the total number of questions in the test, shown as a percentage.", "average_score": "Average Score:", "average_score_desc": "The average percentage score of all candidates for that particular module. Compare candidate's score to the average score to see how they performed relative to peers.", "time_spent": "Time Spent:", "time_spent_desc": "The time spent on each module, given in minutes (e.g. 2 mins/10 mins). This may indicate the difficulty or familiarity with content.", "module_specific_comparative_data_points_desc": "On the top right corner of the results page, there is a dropdown menu that allows you to compare the candidate's score for each module against different benchmarks. These options provide additional context for assessing the candidate's performance at a module specific level:", "your_best_performing_candidate": "Your Best Performing Candidate", "your_best_performing_candidate_desc1": "Compares the candidate's score against the highest score achieved by your best performing candidate for a specific module.", "your_best_performing_candidate_desc2": "Helps to identify how the candidate score compares to the top performer for each module.", "your_candidate_pool_average_desc1": "Compares the candidate's scores to the average score of your candidate pool.", "your_candidate_pool_average_desc2": "Provides insight into how the candidate performs relative to your general pool of candidates.", "dexta_candidate_pool_average": "Dexta Candidate Pool Average", "dexta_candidate_pool_average_desc1": "Compares the candidate's scores to a broader pool average provided by <PERSON><PERSON>.", "dexta_candidate_pool_average_desc2": "Allows for a comparison against a larger, potentially more diverse set of candidates, providing a wider context for the candidate's performance.", "module_scores": "Module Scores", "performance_completion_time_insights_desc": "Provides visual representations of the candidate's performance and completion time in the form of box plots and distribution curves.", "performance_insights": "Performance Insights", "box_plot": "Box Plot:", "box_plot_desc": "Shows the distribution of scores within the candidate pool, including minimum, first quartile (Q1), median, third quartile (Q3), and maximum scores.", "distribution_curve": "Distribution Curve:", "distribution_curve_desc": "Overlays a Gaussian distribution to give a sense of where the candidate's score lies within the overall distribution.", "candidate_score_marker": "Candidate's Score Marker:", "candidate_score_marker_desc": "Indicates the candidate's score on the distribution curve and box plot, helping to visualize their performance relative to others.", "completion_time_insights": "Completion Time Insights", "completion_time_box_plot_desc": "Displays the distribution of completion times, providing insight into how quickly or slowly the candidate completed the test compared to others.", "completion_time_distribution_curve_desc": "Shows a Gaussian distribution of completion times to contextualize the candidate's performance.", "completion_time_marker_desc": "Highlights the candidate's completion time on the distribution curve and box plot.", "anti_cheating_monitor_desc": "This section provides information about the anti-cheating measures during the test.", "device_used": "Device Used:", "device_used_desc": "Indicates the type of device used for the test (e.g., Desktop).", "location": "Location:", "location_desc": "The location from which the test was completed.", "ip_address_check": "IP Address Check:", "ip_address_check_desc": "Indicates whether the test was completed once from the IP address.", "webcam_enabled": "Webcam Enabled:", "webcam_enabled_desc": "States whether the webcam was enabled during the test.", "fullscreen_mode_active": "Full-Screen Mode Active:", "fullscreen_mode_active_desc": "Indicates if full-screen mode was active throughout the test.", "your best performing candidate": "Your best performing candidate", "your candidate pool average": "Your candidate pool average", "dexta candidate pool average": "Dexta candidate pool average", "dexta best performing candidate": "Dexta best performing candidate", "graph_tooltip_scored": "Scored", "graph_tooltip_highest_score": "Highest score", "graph_tooltip_average": "Average", "graph_tooltip_dexta_average": "Dexta average", "graph_tooltip_dexta_best": "<PERSON><PERSON>", "graph_t`ooltip_custom": "Custom"}, "invited": "Invited", "completed": "Completed", "invite_channel": "Invite channel", "hiring_stage": "Hiring stage", "anti_cheating_monitor": "Anti cheating monitor", "device_used": "Device Used", "location": "Location", "test_completed_from_ip": "Test completed once from IP address?", "webcam_enabled": "Webcam enabled?", "fullscreen_mode": "Full-screen mode always active?", "mouse_in_window": "Mouse always in test window?", "images_from_webcam": "Images from webcam", "no_images_available": "No images available", "pending": "Pending", "yes": "Yes", "no": "No", "na": "N/A", "module_scores": "Module Scores", "compare_to": "Compare to", "candidate_pool_comparison": "Candidate Pool Comparison", "analysis_available_after_completion": "Analysis will be made available once candidate completes their test", "insufficient_data_for_analysis": "Insufficient data available for analysis. At least 5 candidates need to complete the test before graphs can be visualized", "activity_log": {"test_created": "Test created", "test_exited": "Test exited", "invite_sent": "<PERSON><PERSON><PERSON> sent", "test_resumed": "Test resumed", "reminder_sent": "<PERSON><PERSON><PERSON> sent", "test_notes": "Test notes", "candidate_hiring_status_updated": "Candidate hiring status updated", "test_updated": "Test updated", "test_started": "Test started", "test_completed": "Test completed"}}, "confirmation": {"page_title": "Confirmation | Dexta", "company_logo_alt": "Company logo", "role_label": "Role", "please_confirm": "Please confirm who you are", "first_name": "First name", "enter_first_name": "Enter your first name", "first_name_required": "First Name Required", "last_name": "Last name", "enter_last_name": "Enter your last name", "last_name_required": "Last Name Required", "email": "Email", "enter_email": "Enter your email", "email_required": "Email is required", "privacy_policy_text": "I have read and I accept the ", "privacy_policy": "Privacy Policy", "privacy_policy_required": "Please review Privacy Policy and check box to proceed", "continue": "Continue", "continuing": "Continuing...", "continue_icon_alt": "Continue icon", "generic_error": "An error occurred.", "invite_expired": "Your invite has expired. Please reach out to your company for further assistance.", "already_completed": "You have already completed this test. If you have not completed this test, please contact and notify {{company}}.", "no_invitation_found": "No invitation found for candidate. Please ask your recruiter to send a new invitation.", "invite_limit": "You can invite only 5 candidates with your current plan!", "test_not_active": "This test is not active. Please contact and notify {{company}}."}, "information": {"page_title": "Information | Dexta", "company_logo_alt": "Company logo", "hello": "Hello", "thank_you": "Thank you for applying to", "welcome_test": "and welcome to our test.", "completing_gives_chance": "Completing it will give you a chance to show off your skills and stand out from the crowd!", "good_luck": "Good luck!", "starting": "Starting...", "get_started": "Get Started", "continue_icon_alt": "Continue icon", "before_you_start": "A few things before you start:", "test_consists_of": "This test consists of", "module": "module", "modules": "modules", "approx_time": "It will take approximately", "minutes": "minutes", "to_complete": "to complete", "test_timed": "The test is timed. A timer is shown as per module or question", "free_to_use": "You are free to use a", "calculator_pen_paper": "calculator, pen and paper", "note_may_be_required": "Note this may only be required for some modules and not all", "preview_questions": "Each test module will have a set of preview questions which are shown before the official test. The preview questions will give you a flavour of what the official test is like", "official_module_starts": "The official module which you will be assessed against will start after the preview questions", "prescribed_time_limit": "Each test module will have its own prescribed time limit. Be sure to monitor the timer as you are completing the test", "allow_camera": "Please allow the use of your camera/webcam and do not leave full-screen mode. Snapshots will be taken of you periodically during the test. These measures are taken to ensure fairness for everyone", "turn_on_speakers": "Turn on your speakers or headphone (to play audio)", "recommend_one_go": "We recommend completing the test in one go", "important_video": "Important Video Message from"}, "camera": {"page_title": "Setup | Dexta", "company_logo_alt": "Company logo", "setup_title": "Camera setup", "setup_description": "We use camera images to ensure fairness for everyone. Make sure that you are in front of your camera.", "camera_label": "Camera", "continue": "Continue", "continue_icon_alt": "Continue icon", "no_camera_message": "It seems you don't have a camera connected to your computer or your camera is blocked. To enable the camera, click on the camera blocked icon in your browser's address bar and reload the page. If you don't enable a camera, you can still take the test, but then {company} cannot verify fair play.", "trouble_title": "Trouble with your webcam?", "trouble_permission": "Ensure you have granted permission for your browser to access your camera.", "trouble_supported_browser": "Ensure you are using a supported browser.", "trouble_multiple_devices": "If you have multiple Camera devices, ensure you have give your browser and our website permission to use the right device.", "trouble_incognito": "Try launching the test in incognito mode or in a private window.", "trouble_update": "Ensure your camera drivers and web browser are up to date.", "trouble_restart": "Restart your device and try accessing the test again using the link in the invitation email.", "close_modal": "Close modal", "denied_title": "Are you sure you want to continue?", "denied_message_1": "You can still take the test if your camera is switched off.", "denied_message_2": "However, switching your camera off will not allow", "denied_message_3": "to verify that you have taken the test fairly.", "cancel": "Cancel"}, "preview-ready": {"page_title": "Get Ready | Dexta", "company_logo_alt": "Company logo", "practice_module": "This is the practice module:", "module_will_start_in": "The module will start in", "seconds": "seconds", "stay_on_screen": "Please stay on this screen. The timer for your next module has started, and it cannot be paused."}, "moduleFeedback": {"company_logo_alt": "Company logo", "finished_module": "You have now finished the ", "first": "first ", "second": "second ", "third": "third ", "fourth": "fourth ", "fifth": "fifth ", "module": "module", "help_improve": "Help us improve this module by providing anonymous feedback. Your answer will be available to <PERSON><PERSON>, not your prospective employer.", "accurately_measure": "In your opinion, did the module accurately measure your skills in", "yes": "Yes", "somewhat": "Somewhat", "no": "No", "please_explain": "Please explain...", "submit": "Submit", "submit_icon_alt": "Submit icon"}, "preview-question": {"submit": "Submit", "next": "Next", "next_icon_alt": "Next Step", "select_only_one": "SELECT ONLY ONE", "select_all": "SELECT ALL THAT APPLY", "insert_answer": "Insert answer in highlighted cell", "practice_question": "This is the", "question": "QUESTION"}, "get-ready": {"module_number": "Module Number:", "out_of": "out of", "module_will_start_in": "The module will start in", "seconds": "seconds", "please_stay": "Please stay on this screen. The timer for your next module has started, and it cannot be paused."}, "test-screen": {"back": "Back", "skip": "<PERSON><PERSON>", "submit": "Submit", "continue": "Continue", "question": "QUESTION", "of": "of", "select_only_one": "SELECT ONLY ONE", "select_all_that_apply": "SELECT ALL THAT APPLY", "skip_question_modal_title": "Are you sure you want to skip this question?", "skip_question_modal_desc": "You may skip this question if the answer is not immediately apparent. Feel free to return later and provide your response.", "skip_question": "Skip question", "skipping": "Skipping", "choose_answer": "Choose answer", "cannot_go_back": "You cannot go back to the previous question", "navigation_restricted": "You have resumed this test, so navigation is restricted.", "insert_answer_excel": "Insert answer in highlighted cell"}, "feedback": {"privacy_policy": "Privacy Policy", "info_text": "To help make sure that our test provide equal opportunities for everyone. We statistically analyse test results anonymously. By sharing this information about your background, you help us improve our tests for all candidates. This information will not be shared with your potential employer. Sharing this information with us is optional, and anything shared will be held in compliance with our", "education_label": "Highest level of education", "education_placeholder": "Select your highest education", "study_placeholder": "What did you study", "field_label": "Field of study", "field_placeholder": "Select your field of study", "years_experience_label": "Years of experience in role", "years_experience_placeholder": "Years of experience in role", "gender_label": "Gender", "gender_placeholder": "Gender", "gender_other": "Other (Please specify)", "gender_other_placeholder": "Please specify", "born_year_label": "What year were you born?", "born_year_placeholder": "What year were you born?", "country_label": "Country/Region of Residence", "country_placeholder": "Country/Region of Residence", "country_add": "Add \"{{country}}\"", "ethnicity_label": "Ethnicity", "ethnicity_placeholder": "Ethnicity", "candidate_status_label": "Candidate Status", "candidate_status_placeholder": "Candidate Status", "candidate_status_other": "Other (Please specify)", "candidate_status_other_placeholder": "Please specify", "language_label": "What is your first language", "language_placeholder": "What is your first language", "language_other": "Other", "language_other_placeholder": "Please specify", "finished_test_experience": "You've finished your test. How was your experience?", "thank_you": "Thank You", "explain_placeholder": "Please explain...", "education_options": {"primary": "Primary Education", "secondary": "Secondary Education (High School)", "vocational": "Vocational/Technical Training", "no_degree": "College / University (No Degree)", "associate": "Associate Degree", "bachelor": "Bachelor's Degree", "master": "Master's Degree", "professional": "Professional Degree (e.g., MD, JD)", "doctorate": "Doctorate (PhD)", "other": "Other (Please specify)"}, "gender_options": {"male": "Male", "female": "Female", "non_binary": "Non-binary", "prefer_not_say": "Prefer not to say", "other": "Other (Please specify)"}, "candidate_status_options": {"employed_full_time": "Employed Full-Time", "employed_part_time": "Employed Part-Time", "self_employed": "Self-Employed", "unemployed": "Unemployed", "student": "Student", "internship": "Internship", "freelancer": "Freelancer/Contractor", "retired": "Retired", "homemaker": "Homemaker", "prefer_not_say": "Prefer not to say", "other": "Other (Please specify)"}, "ethnicity_options": {"arab": "Arab", "asian": "Asian", "black": "Black, Caribbean, or African", "hispanic": "Hispanic or Latin American", "mixed": "Mixed or multiple ethnic groups", "pacific": "Pacific Islander", "other_ethnicity": "Other ethnicity", "white": "White", "prefer_not_answer": "Prefer not to answer"}, "language_options": {"english": "English", "danish": "Danish", "dutch": "Dutch", "french": "French", "hindi": "Hindi", "arabic": "Arabic", "cantonese": "Cantonese", "mandarin": "Mandarin", "german": "German", "italian": "Italian", "norwegian": "Norwegian", "polish": "Polish", "portuguese": "Portuguese (Brazil)", "spanish": "Spanish", "swedish": "Swedish", "other": "Other", "prefer_not_answer": "Prefer not to answer"}, "experience_years_options": {"1": "1 year", "2": "2 years", "3": "3 years", "4": "4 years", "5": "5 years", "6": "6 years", "7": "7 years", "8": "8 years", "9": "9 years", "10": "10 years", "11": "11 years", "12": "12 years", "13": "13 years", "14": "14 years", "15": "15 years", "16": "16 years", "17": "17 years", "18": "18 years", "19": "19 years", "20": "20 years"}}, "finished": {"title": "Great job! You've completed your test", "description": "Would you like to see your results now? We will redirect you to create your Dexta Profile and view your test outcome", "redirecting": "Redirecting in", "seconds": "seconds", "go_now": "Go there now"}, "Register_Candidate": {"title": "Create your Dex<PERSON> profile", "subtitle": "Enter the email address to which you received a test invite.", "first_name_placeholder": "Enter first name", "last_name_placeholder": "Enter last name", "email_placeholder": "Enter your email", "password_placeholder": "Password", "gender_label": "Gender", "gender_placeholder": "Gender", "gender_other": "Other (Please specify)", "gender_other_placeholder": "Please specify", "gender_required": "Gender Required", "field_label": "Most relevant experience", "field_placeholder": "Most relevant experience", "field_required": "Most relevant experience Required", "experience_years_label": "Years of experience in field", "experience_years_placeholder": "Years of experience in field", "experience_years_required": "Years of experience Required", "add_field": "Add \"{{field}}\"", "sign_up": "Sign up", "signing_up": "Signing up", "already_have_account": "Already have an account?", "log_in_here": "Log in here", "terms": "candidate terms", "privacy_policy": "Privacy Policy", "accept_terms": "I have read and accepted the", "and": "and", "password_number": "contains at least one number", "password_upper": "contains at least one upper character", "password_special": "contains at least one special character", "password_length": "contains at least 12 characters", "dropdown_gender_options": {"male": "Male", "female": "Female", "non_binary": "Non-binary", "prefer_not_say": "Prefer not to say", "other": "Other (Please specify)"}, "dropdown_experience_years_options": {"1": "1 year", "2": "2 years", "3": "3 years", "4": "4 years", "5": "5 years", "6": "6 years", "7": "7 years", "8": "8 years", "9": "9 years", "10": "10 years", "11": "11 years", "12": "12 years", "13": "13 years", "14": "14 years", "15": "15 years", "16": "16 years", "17": "17 years", "18": "18 years", "19": "19 years", "20": "20 years"}}, "Login_Candidate": {"title": "Log in to your Dexta Profile", "email_placeholder": "Enter your email", "password_placeholder": "Password", "keep_logged_in": "Keep me logged in", "forgot_password": "Forgot password?", "log_in": "Log in", "logging_in": "Logging in", "dont_have_account": "Don't have an account?", "create_one_here": "Create one here", "email_required": "Email Required", "password_required": "Password Required", "invalid_email": "Invalid email address", "error_message": "Invalid email or password. Please try again."}, "Candidate-Profile": {"page_title": "Candidate Dashboard | Dexta", "my_results": "My results", "no_results": "Your test results will be displayed here once you have taken a test", "completed": "Completed", "invited": "Invited", "started": "Started", "in_progress": "In Progress", "company_name": "Company name", "invited_on": "Invited on", "last_activity": "Last activity", "resume_test": "Resume test", "yes": "Yes", "resume_test_confirmation": "Are you sure you want to resume your test?"}, "Candidate-Results": {"for": "for", "completed": "Completed", "in_progress": "In Progress", "invited_for_test_on": "Invited for Test on", "resume_test": "Resume test", "start_test": "Start test", "not_reflect_overall_progress": "The results of this test do not reflect your overall progress through the recruitment process.", "not_started": "The candidate did not start the evaluation yet.", "your_progress": "Your progress", "modules": "Modules:", "out_of": "out of", "time_left_on_current_module": "Time left on current module:", "minutes": "minutes", "tests": "Tests", "no_work_started": "No work has started on any module for the test yet.", "module_name": "Module name", "your_score": "Your score", "companies_compare_scores": "Companies will compare your scores with other candidates. So if your score looks low, don't worry and wait for the company to contact you. You may have still done better than the other candidates.", "dexta_candidate_pool_average": "Dexta candidate pool average", "your_test_completion_time": "Your Test completion time", "dexta_average_completion_time": "Dexta average completion time", "yes": "Yes", "start_test_confirmation": "Are you sure you want to start your test?", "resume_test_confirmation": "Are you sure you want to resume your test?", "seconds": "seconds"}, "Candidate-Settings": {"settings": "Settings", "tabs_general": "General", "tabs_education": "Education", "tabs_experience": "Work experience", "profile_picture": "Profile picture", "upload": "Upload", "personal_information": "Personal information", "first_name": "First name", "last_name": "Last name", "phone_number": "Phone number", "gender": "Gender", "gender_male": "Male", "gender_female": "Female", "gender_non_binary": "Non-binary", "gender_prefer_not_to_say": "Prefer not to say", "gender_other": "Other (Please specify)", "please_specify": "Please specify", "country": "Country", "education_information": "Education information", "highest_education_level": "Highest education level", "what_did_you_study": "What did you study?", "language": "Language", "work_experience": "Work experience experience", "most_relevant_experience": "Most relevant experience", "primary_role": "Primary Role", "years_of_experience_in_field": "Years of experience in field", "location_preferences": "Location preferences", "candidate_status": "Candidate status", "desired_gross_annual_salary": "Desired gross annual salary", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount_per_year": "Amount/yr", "save": "Save", "saving": "Saving...", "add": "Add", "english": "English", "change_password": "Change password", "change_email": "Change email", "experience_years_1_year": "1 year", "experience_years_2_years": "2 years", "experience_years_3_years": "3 years", "experience_years_4_years": "4 years", "experience_years_5_years": "5 years", "experience_years_6_years": "6 years", "experience_years_7_years": "7 years", "experience_years_8_years": "8 years", "experience_years_9_years": "9 years", "experience_years_10_years": "10 years", "experience_years_11_years": "11 years", "experience_years_12_years": "12 years", "experience_years_13_years": "13 years", "experience_years_14_years": "14 years", "experience_years_15_years": "15 years", "experience_years_16_years": "16 years", "experience_years_17_years": "17 years", "experience_years_18_years": "18 years", "experience_years_19_years": "19 years", "experience_years_20_years": "20 years", "experience_years_21_years": "21 years", "experience_years_22_years": "22 years", "experience_years_23_years": "23 years", "experience_years_24_years": "24 years", "experience_years_25_years": "25 years", "experience_years_26_years": "26 years", "experience_years_27_years": "27 years", "experience_years_28_years": "28 years", "experience_years_29_years": "29 years", "experience_years_30_years": "30 years", "candidate_status_employed_full_time": "Employed Full-Time", "candidate_status_employed_part_time": "Employed Part-Time", "candidate_status_self_employed": "Self-Employed", "candidate_status_unemployed": "Unemployed", "candidate_status_student": "Student", "candidate_status_internship": "Internship", "candidate_status_freelancer_contractor": "Freelancer/Contractor", "candidate_status_retired": "Retired", "candidate_status_homemaker": "Homemaker", "candidate_status_prefer_not_to_say": "Prefer not to say", "candidate_status_other_please_specify": "Other (Please specify)", "location_remote": "Remote", "location_on_site": "On/Site", "location_hybrid": "Hybrid", "currency_usd": "USD", "currency_eur": "EUR", "currency_gbp": "GBP", "currency_aud": "AUD", "currency_sar": "SAR", "amount_yr": "Amount/yr"}, "Access-Denied": {"enter_email_address": "Enter email address", "returning_info": "If returning to a test you started previously, please use the same email address to log back into the test", "support_contact": "For support, please contact <1>{{email}}</1>", "email_label": "Email", "email_placeholder": "Enter your email", "verifying_email": "Verifying email...", "continue": "Continue"}, "Navbar-Candidate": {"edit_profile_info": "Edit profile info", "logout": "Logout", "open_main_menu": "Open main menu", "my_tests": "My Tests", "my_profile": "My Profile"}, "Education-Info": {"education_information": "Education Information", "highest_education_level": "Highest education level", "please_specify": "Please specify", "what_did_you_study": "What did you study?", "language": "Language", "save": "Save", "saving": "Saving...", "dropdown": {"Primary_Education": "Primary Education", "Secondary_Education_High_School": "Secondary Education (High School)", "Vocational_Technical_Training": "Vocational/Technical Training", "College___University_No_Degree": "College / University (No Degree)", "Associate_Degree": "Associate Degree", "Bachelor_s_Degree": "Bachelor's Degree", "Master_s_Degree": "Master's Degree", "Professional_Degree_e_g__MD_JD": "Professional Degree (e.g., MD, JD)", "Doctorate_PhD": "Doctorate (PhD)", "Other_Please_specify": "Other (Please specify)"}}, "invite_expired_heading": "Invite Expired", "invalid_code_heading": "Invalid Code", "invalid_code_message": "Oops! It seems like the code you provided is not valid. Please check your code and try again."}